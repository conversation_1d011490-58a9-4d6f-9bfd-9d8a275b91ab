using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using SinterOptimizationClient.Models;
using SinterOptimizationClient.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SinterOptimizationClient.ViewModels
{
    public partial class ResultViewModel : ObservableObject
    {
        private readonly IDialogService _dialogService;

        [ObservableProperty]
        private OptimizationResult? currentResult;

        [ObservableProperty]
        private ObservableCollection<ResultSolution> costOptimalSolutions = new();

        [ObservableProperty]
        private ObservableCollection<ResultSolution> qualityOptimalSolutions = new();

        [ObservableProperty]
        private ObservableCollection<ResultSolution> currentSolutions = new();

        [ObservableProperty]
        private ResultSolution? selectedSolution;

        [ObservableProperty]
        private bool isCostOptimalSelected = true;

        [ObservableProperty]
        private bool isQualityOptimalSelected = false;

        [ObservableProperty]
        private string algorithmInfo = "SQP二次序列算法";

        [ObservableProperty]
        private string algorithmAccuracy = "计算偏差≤0.02%（成分）、≤0.01（碱度）";

        [ObservableProperty]
        private string calculationLogic = "基于干料量 = 配料比 ×(1 - 物理水)、烧成量 = 干料量 ×(1 - 烧损率) 推导，各成分含量 =Σ(原料成分 × 干料量)/Σ 烧成量 ×100";

        [ObservableProperty]
        private bool isFormulaExpanded = true;

        [ObservableProperty]
        private string statusMessage = "暂无优化结果";

        public ResultViewModel(IDialogService dialogService)
        {
            _dialogService = dialogService;

            // 初始化状态消息
            StatusMessage = "暂无优化结果，请先进行优化计算";

            // 初始化空的解决方案集合
            CostOptimalSolutions = new ObservableCollection<ResultSolution>();
            QualityOptimalSolutions = new ObservableCollection<ResultSolution>();
            CurrentSolutions = new ObservableCollection<ResultSolution>();
        }

        public void SetOptimizationResult(OptimizationResult result)
        {
            try
            {
                CurrentResult = result;
                LoadSolutions();
                StatusMessage = result?.Success == true ? "优化计算完成" : "优化计算失败";
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载优化结果时发生错误: {ex.Message}";
                _dialogService?.ShowError("错误", $"加载优化结果时发生错误: {ex.Message}");
            }
        }

        private void LoadSolutions()
        {
            try
            {
                CostOptimalSolutions.Clear();
                QualityOptimalSolutions.Clear();
                CurrentSolutions.Clear();

                if (CurrentResult?.Success == true)
                {
                    var solutions = CurrentResult.GetSolutions();

                    if (solutions != null)
                    {
                        foreach (var solution in solutions)
                        {
                            if (solution?.OptimizationType != null)
                            {
                                if (solution.OptimizationType.Contains("cost") || solution.OptimizationType.Contains("成本"))
                                {
                                    CostOptimalSolutions.Add(solution);
                                }
                                else if (solution.OptimizationType.Contains("quality") || solution.OptimizationType.Contains("质量"))
                                {
                                    QualityOptimalSolutions.Add(solution);
                                }
                            }
                        }

                        // 默认显示成本最优结果
                        if (CostOptimalSolutions.Any())
                        {
                            IsCostOptimalSelected = true;
                            IsQualityOptimalSelected = false;

                            foreach (var solution in CostOptimalSolutions)
                            {
                                CurrentSolutions.Add(solution);
                            }

                            SelectedSolution = CurrentSolutions.FirstOrDefault();
                            StatusMessage = $"当前显示: 成本最优结果 ({CurrentSolutions.Count} 个方案)";
                        }
                        else if (QualityOptimalSolutions.Any())
                        {
                            IsCostOptimalSelected = false;
                            IsQualityOptimalSelected = true;

                            foreach (var solution in QualityOptimalSolutions)
                            {
                                CurrentSolutions.Add(solution);
                            }

                            SelectedSolution = CurrentSolutions.FirstOrDefault();
                            StatusMessage = $"当前显示: 质量最优结果 ({CurrentSolutions.Count} 个方案)";
                        }
                        else
                        {
                            StatusMessage = "暂无可显示的优化结果";
                        }
                    }
                    else
                    {
                        StatusMessage = "优化结果数据为空";
                    }
                }
                else
                {
                    StatusMessage = CurrentResult?.ErrorMessage ?? "暂无优化结果";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载解决方案时发生错误: {ex.Message}";
                _dialogService?.ShowError("错误", $"加载解决方案时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private void SwitchToCostOptimal()
        {
            try
            {
                IsCostOptimalSelected = true;
                IsQualityOptimalSelected = false;

                // 更新当前显示的解决方案
                CurrentSolutions.Clear();
                foreach (var solution in CostOptimalSolutions)
                {
                    CurrentSolutions.Add(solution);
                }

                SelectedSolution = CurrentSolutions.FirstOrDefault();
                StatusMessage = $"当前显示: 成本最优结果 ({CurrentSolutions.Count} 个方案)";
            }
            catch (Exception ex)
            {
                StatusMessage = $"切换到成本最优结果时发生错误: {ex.Message}";
                _dialogService?.ShowError("错误", $"切换到成本最优结果时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private void SwitchToQualityOptimal()
        {
            try
            {
                IsCostOptimalSelected = false;
                IsQualityOptimalSelected = true;

                // 更新当前显示的解决方案
                CurrentSolutions.Clear();
                foreach (var solution in QualityOptimalSolutions)
                {
                    CurrentSolutions.Add(solution);
                }

                SelectedSolution = CurrentSolutions.FirstOrDefault();
                StatusMessage = $"当前显示: 质量最优结果 ({CurrentSolutions.Count} 个方案)";
            }
            catch (Exception ex)
            {
                StatusMessage = $"切换到质量最优结果时发生错误: {ex.Message}";
                _dialogService?.ShowError("错误", $"切换到质量最优结果时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private void ViewSolutionDetails(ResultSolution? solution)
        {
            if (solution == null) return;

            var details = BuildSolutionDetails(solution);
            _dialogService.ShowInformation($"方案 {solution.SolutionId} 详细信息", details);
        }

        [RelayCommand]
        private void ToggleFormulaExpansion()
        {
            IsFormulaExpanded = !IsFormulaExpanded;
        }

        [RelayCommand]
        private async Task ExportResults()
        {
            try
            {
                if (CurrentResult == null || !CurrentResult.Success)
                {
                    _dialogService.ShowWarning("导出警告", "暂无可导出的优化结果");
                    return;
                }

                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出优化结果",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx|CSV文件 (*.csv)|*.csv",
                    DefaultExt = "xlsx",
                    FileName = $"优化结果_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    StatusMessage = "正在导出结果...";

                    var extension = Path.GetExtension(saveFileDialog.FileName).ToLower();
                    if (extension == ".csv")
                    {
                        await ExportToCsv(saveFileDialog.FileName);
                    }
                    else
                    {
                        await ExportToExcel(saveFileDialog.FileName);
                    }

                    StatusMessage = "结果导出完成";
                    _dialogService.ShowInformation("导出成功", $"优化结果已导出到: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "导出失败";
                _dialogService.ShowError("导出错误", $"导出结果时发生错误: {ex.Message}");
            }
        }

        [RelayCommand]
        private void RefreshResults()
        {
            if (CurrentResult != null)
            {
                LoadSolutions();
                StatusMessage = "结果已刷新";
            }
        }

        private string BuildSolutionDetails(ResultSolution solution)
        {
            var details = new StringBuilder();
            details.AppendLine($"方案编号: {solution.SolutionId}");
            details.AppendLine($"优化类型: {solution.OptimizationType}");
            details.AppendLine($"目标函数值: {solution.ObjectiveValue:F6}");
            details.AppendLine($"迭代次数: {solution.Iterations}");
            details.AppendLine();

            details.AppendLine("湿配比详情:");
            foreach (var ratio in solution.WetRatios.OrderByDescending(kvp => kvp.Value))
            {
                if (ratio.Value > 0.01)
                {
                    details.AppendLine($"  {ratio.Key}: {ratio.Value:F2}%");
                }
            }
            details.AppendLine();

            details.AppendLine("烧结矿成分:");
            details.AppendLine($"  TFe: {solution.TFe:F2}%");
            details.AppendLine($"  碱度R: {solution.R:F3}");
            details.AppendLine($"  MgO: {solution.MgO:F2}%");
            details.AppendLine($"  Al₂O₃: {solution.Al2O3:F2}%");
            details.AppendLine();

            details.AppendLine($"单位成本: {solution.Cost:F2} 元/吨");

            if (solution.OptimizationType.Contains("quality") || solution.OptimizationType.Contains("质量"))
            {
                details.AppendLine();
                details.AppendLine("质量偏差:");
                details.AppendLine($"  TFe偏差: {solution.FormattedTFeDeviation}%");
                details.AppendLine($"  R偏差: {solution.FormattedRDeviation}");
            }

            return details.ToString();
        }

        private async Task ExportToCsv(string fileName)
        {
            var csv = new StringBuilder();

            // 添加表头
            csv.AppendLine("方案,优化类型,湿配比,成本(元/吨),TFe(%),碱度R,MgO(%),Al2O3(%),TFe偏差(%),R偏差,目标函数值,迭代次数");

            // 添加成本最优结果
            foreach (var solution in CostOptimalSolutions)
            {
                csv.AppendLine($"{solution.SolutionId},{solution.OptimizationType},\"{solution.FormattedWetRatios}\",{solution.Cost:F2},{solution.TFe:F2},{solution.R:F3},{solution.MgO:F2},{solution.Al2O3:F2},{solution.FormattedTFeDeviation},{solution.FormattedRDeviation},{solution.ObjectiveValue:F6},{solution.Iterations}");
            }

            // 添加质量最优结果
            foreach (var solution in QualityOptimalSolutions)
            {
                csv.AppendLine($"{solution.SolutionId},{solution.OptimizationType},\"{solution.FormattedWetRatios}\",{solution.Cost:F2},{solution.TFe:F2},{solution.R:F3},{solution.MgO:F2},{solution.Al2O3:F2},{solution.FormattedTFeDeviation},{solution.FormattedRDeviation},{solution.ObjectiveValue:F6},{solution.Iterations}");
            }

            await File.WriteAllTextAsync(fileName, csv.ToString(), Encoding.UTF8);
        }

        private async Task ExportToExcel(string fileName)
        {
            // 这里应该实现Excel导出功能
            // 暂时使用CSV格式
            await ExportToCsv(fileName.Replace(".xlsx", ".csv"));
        }

        // 计算公式集合
        public List<string> CalculationFormulas => new()
        {
            "干料量 = 配料比（%）×（1 - 物理水（%）/100）",
            "总干料量 = Σ 各原料干料量",
            "烧成量 = 干料量 ×（1 - 烧损率（%）/100）",
            "TFe（%）= Σ（原料干料量 × 原料TFe（%））/Σ 烧成量 ×100",
            "Ro = ΣCaO/ΣSiO₂ 或 (ΣCaO+ΣMgO)/(ΣSiO₂+ΣAl₂O₃)"
        };
    }
}
