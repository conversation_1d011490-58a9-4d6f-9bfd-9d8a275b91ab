using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SinterOptimizationClient.Models
{
    /// <summary>
    /// 优化结果模型
    /// </summary>
    public partial class OptimizationResult : ObservableObject
    {
        [ObservableProperty]
        private bool success;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private string optimizationType = string.Empty;

        [ObservableProperty]
        private Dictionary<string, double> optimalRatios = new();

        [ObservableProperty]
        private SinterProperties sinterProperties = new();

        [ObservableProperty]
        private double unitCost;

        [ObservableProperty]
        private double objectiveValue;

        [ObservableProperty]
        private int iterations;

        [ObservableProperty]
        private string message = string.Empty;

        [ObservableProperty]
        private DateTime timestamp = DateTime.Now;

        [ObservableProperty]
        private OptimizationResult? alternativeSolution;

        // 生成结果方案列表
        public List<ResultSolution> GetSolutions()
        {
            var solutions = new List<ResultSolution>();

            if (Success)
            {
                solutions.Add(new ResultSolution
                {
                    SolutionId = 1,
                    OptimizationType = OptimizationType,
                    WetRatios = OptimalRatios,
                    Cost = UnitCost,
                    TFe = SinterProperties.TFe,
                    R = SinterProperties.R,
                    MgO = SinterProperties.MgO,
                    Al2O3 = SinterProperties.Al2O3,
                    TFeDeviation = CalculateTFeDeviation(),
                    RDeviation = CalculateRDeviation(),
                    ObjectiveValue = ObjectiveValue,
                    Iterations = Iterations
                });

                if (AlternativeSolution?.Success == true)
                {
                    solutions.Add(new ResultSolution
                    {
                        SolutionId = 2,
                        OptimizationType = AlternativeSolution.OptimizationType,
                        WetRatios = AlternativeSolution.OptimalRatios,
                        Cost = AlternativeSolution.UnitCost,
                        TFe = AlternativeSolution.SinterProperties.TFe,
                        R = AlternativeSolution.SinterProperties.R,
                        MgO = AlternativeSolution.SinterProperties.MgO,
                        Al2O3 = AlternativeSolution.SinterProperties.Al2O3,
                        TFeDeviation = AlternativeSolution.CalculateTFeDeviation(),
                        RDeviation = AlternativeSolution.CalculateRDeviation(),
                        ObjectiveValue = AlternativeSolution.ObjectiveValue,
                        Iterations = AlternativeSolution.Iterations
                    });
                }
            }

            return solutions;
        }

        private double CalculateTFeDeviation()
        {
            // 这里应该根据目标值计算偏差
            // 暂时返回0，实际应该从优化参数中获取目标值
            return 0;
        }

        private double CalculateRDeviation()
        {
            // 这里应该根据目标值计算偏差
            // 暂时返回0，实际应该从优化参数中获取目标值
            return 0;
        }
    }

    /// <summary>
    /// 烧结矿性质
    /// </summary>
    public partial class SinterProperties : ObservableObject
    {
        [ObservableProperty]
        private double tFe;

        [ObservableProperty]
        private double r;

        [ObservableProperty]
        private double mgO;

        [ObservableProperty]
        private double al2O3;

        [ObservableProperty]
        private double caO;

        [ObservableProperty]
        private double siO2;

        [ObservableProperty]
        private double tiO2;
    }

    /// <summary>
    /// 结果方案
    /// </summary>
    public partial class ResultSolution : ObservableObject
    {
        [ObservableProperty]
        private int solutionId;

        [ObservableProperty]
        private string optimizationType = string.Empty;

        [ObservableProperty]
        private Dictionary<string, double> wetRatios = new();

        [ObservableProperty]
        private double cost;

        [ObservableProperty]
        private double tFe;

        [ObservableProperty]
        private double r;

        [ObservableProperty]
        private double mgO;

        [ObservableProperty]
        private double al2O3;

        [ObservableProperty]
        private double tFeDeviation;

        [ObservableProperty]
        private double rDeviation;

        [ObservableProperty]
        private double objectiveValue;

        [ObservableProperty]
        private int iterations;

        // 格式化湿配比显示
        public string FormattedWetRatios
        {
            get
            {
                if (WetRatios == null || !WetRatios.Any())
                    return "无数据";

                var ratios = WetRatios
                    .Where(kvp => kvp.Value > 0.01) // 只显示大于0.01%的配比
                    .OrderByDescending(kvp => kvp.Value)
                    .Select(kvp => $"{kvp.Key}:{kvp.Value:F1}%");

                return string.Join(", ", ratios);
            }
        }

        // 格式化偏差显示
        public string FormattedTFeDeviation => TFeDeviation >= 0 ? $"+{TFeDeviation:F2}" : $"{TFeDeviation:F2}";
        public string FormattedRDeviation => RDeviation >= 0 ? $"+{RDeviation:F3}" : $"{RDeviation:F3}";
    }

    /// <summary>
    /// 优化请求模型
    /// </summary>
    public class OptimizationRequest
    {
        public List<RawMaterialData> RawMaterials { get; set; } = new();
        public OptimizationTarget Target { get; set; } = new();
        public OptimizationConstraints Constraints { get; set; } = new();
        public string OptimizeType { get; set; } = "cost";
        public bool MultiSolution { get; set; } = true;
    }

    public class RawMaterialData
    {
        public string Name { get; set; } = string.Empty;
        public double Tfe { get; set; }
        public double Cao { get; set; }
        public double Sio2 { get; set; }
        public double Mgo { get; set; }
        public double Al2o3 { get; set; }
        public double H2o { get; set; }
        public double Ig { get; set; }
        public double Price { get; set; }
        public double MinRatio { get; set; }
        public double MaxRatio { get; set; }
    }

    public class OptimizationTarget
    {
        public double TfeTarget { get; set; }
        public double RoTarget { get; set; }
        public double MgoTarget { get; set; }
        public double Al2o3Target { get; set; }
    }

    public class OptimizationConstraints
    {
        public (double Min, double Max) TfeRange { get; set; }
        public (double Min, double Max) RoRange { get; set; }
        public (double Min, double Max) MgoRange { get; set; }
        public (double Min, double Max) Al2o3Range { get; set; }
        public (double Min, double Max) CostRange { get; set; }
    }
}
