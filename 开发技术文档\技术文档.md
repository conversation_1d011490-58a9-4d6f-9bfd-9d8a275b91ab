# 烧结矿成分及指标计算系统 - 技术文档

## 目录
1. [系统架构](#系统架构)
2. [技术栈](#技术栈)
3. [核心算法](#核心算法)
4. [API接口](#api接口)
5. [数据模型](#数据模型)
6. [部署指南](#部署指南)
7. [开发指南](#开发指南)

## 系统架构

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Presentation)                    │
├─────────────────────────────────────────────────────────────┤
│  C# WPF Application                                        │
│  ├── Views (XAML)                                          │
│  ├── ViewModels (MVVM)                                     │
│  ├── Models                                                │
│  └── Services (HTTP Client)                                │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/JSON
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    服务层 (Service)                          │
├─────────────────────────────────────────────────────────────┤
│  Python Flask Application                                  │
│  ├── REST API Endpoints                                    │
│  ├── Request/Response Handlers                             │
│  ├── Data Validation                                       │
│  └── Error Handling                                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    算法层 (Algorithm)                        │
├─────────────────────────────────────────────────────────────┤
│  SQP Optimization Engine                                   │
│  ├── Objective Function Builder                            │
│  ├── Constraint Handler                                    │
│  ├── SciPy SLSQP Solver                                    │
│  └── Result Formatter                                      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据层 (Data)                            │
├─────────────────────────────────────────────────────────────┤
│  ├── Material Database (JSON/CSV)                          │
│  ├── Configuration Storage                                 │
│  ├── Calculation Results Cache                             │
│  └── Export/Import Handlers                                │
└─────────────────────────────────────────────────────────────┘
```

### 通信流程
1. **用户交互**: 用户在WPF界面输入参数
2. **数据验证**: 客户端验证输入数据完整性
3. **HTTP请求**: 通过HTTP POST发送优化请求
4. **算法计算**: Flask服务调用SQP算法进行优化
5. **结果返回**: 将优化结果以JSON格式返回
6. **界面更新**: 客户端解析结果并更新界面

## 技术栈

### 前端技术栈
- **框架**: WPF (.NET 6.0)
- **架构模式**: MVVM (Model-View-ViewModel)
- **UI库**: MaterialDesignThemes
- **数据绑定**: CommunityToolkit.Mvvm
- **HTTP通信**: HttpClient + Newtonsoft.Json
- **依赖注入**: Microsoft.Extensions.DependencyInjection

### 后端技术栈
- **框架**: Flask 2.3.3
- **算法库**: SciPy 1.11.1 (SLSQP优化器)
- **数值计算**: NumPy 1.24.3
- **跨域支持**: Flask-CORS 4.0.0
- **数据格式**: JSON

### 开发工具
- **IDE**: Visual Studio 2022 / VS Code
- **版本控制**: Git
- **包管理**: NuGet (C#) + pip (Python)
- **测试工具**: 集成测试脚本

## 核心算法

### SQP算法实现

#### 算法原理
SQP (Sequential Quadratic Programming) 是一种求解非线性约束优化问题的有效方法：

```
minimize    f(x)
subject to  g_i(x) ≤ 0,  i = 1, ..., m
           h_j(x) = 0,   j = 1, ..., p
           x_l ≤ x ≤ x_u
```

#### 目标函数设计

**成本最优目标函数**:
```python
def cost_objective(ratios):
    unit_cost = sum(ratio * material.price for ratio, material in zip(ratios, materials))
    quality_penalty = sum(weight * (actual - target)^2 for component in components)
    return unit_cost + quality_penalty
```

**质量最优目标函数**:
```python
def quality_objective(ratios):
    tfe_deviation = (actual_tfe - target_tfe)^2
    r_deviation = (actual_r - target_r)^2
    mgo_deviation = (actual_mgo - target_mgo)^2
    cost_penalty = 0.1 * unit_cost / 1000.0
    return tfe_deviation + r_deviation + mgo_deviation + cost_penalty
```

#### 约束条件实现

**等式约束** (配比总和):
```python
def ratio_sum_constraint(ratios):
    return sum(ratios) - 100.0  # 总配比必须等于100%
```

**不等式约束** (成分范围):
```python
def component_constraints(ratios):
    sinter_properties = calculate_sinter_properties(ratios)
    constraints = []
    
    # TFe范围约束
    constraints.append(sinter_properties['tfe'] - tfe_min)  # tfe >= tfe_min
    constraints.append(tfe_max - sinter_properties['tfe'])  # tfe <= tfe_max
    
    # 其他成分约束...
    return constraints
```

#### 计算公式

**干料量计算**:
```
干料量 = 配料比(%) × (1 - 物理水(%) / 100)
```

**烧成量计算**:
```
烧成量 = 干料量 × (1 - 烧损率(%) / 100)
```

**成分含量计算**:
```
TFe(%) = Σ(原料干料量 × 原料TFe(%)) / Σ烧成量 × 100
```

**碱度计算**:
```
R = ΣCaO / ΣSiO₂  或  R = (ΣCaO + ΣMgO) / (ΣSiO₂ + ΣAl₂O₃)
```

## API接口

### 基础接口

#### 健康检查
```http
GET /health
```

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-30T10:00:00",
  "service": "SQP优化服务"
}
```

#### 服务信息
```http
GET /
```

**响应**:
```json
{
  "message": "烧结配料计算模型系统",
  "version": "2.0.0",
  "status": "running",
  "endpoints": {
    "solve": "/api/solve",
    "health": "/health"
  }
}
```

### 优化接口

#### 优化计算
```http
POST /api/solve
Content-Type: application/json
```

**请求体**:
```json
{
  "raw_materials": [
    {
      "name": "碱性精粉",
      "tfe": 63.76,
      "cao": 1.94,
      "sio2": 4.95,
      "mgo": 1.85,
      "al2o3": 0.60,
      "h2o": 8.20,
      "ig": 1.23,
      "price": 752.21,
      "min_ratio": 0,
      "max_ratio": 30
    }
  ],
  "target": {
    "tfe_target": 55.0,
    "ro_target": 1.90,
    "mgo_target": 2.39,
    "al2o3_target": 1.89
  },
  "constraints": {
    "tfe_range": [54, 56],
    "ro_range": [1.88, 2.02],
    "mgo_range": [1.8, 3.0],
    "al2o3_range": [1.5, 2.5],
    "cost_range": [600, 680]
  },
  "optimize_type": "cost",
  "multi_solution": true
}
```

**响应**:
```json
{
  "success": true,
  "optimization_type": "cost",
  "optimal_ratios": {
    "碱性精粉": 25.5,
    "酸性精粉": 20.3,
    "印粉海娜": 15.2
  },
  "sinter_properties": {
    "tfe": 55.2,
    "r": 1.92,
    "mgo": 2.4,
    "al2o3": 1.8,
    "cao": 11.1,
    "sio2": 5.8,
    "tio2": 0.23
  },
  "unit_cost": 665.5,
  "objective_value": 0.665,
  "iterations": 15,
  "message": "优化成功",
  "timestamp": "2024-01-30T10:05:00",
  "alternative_solution": {
    "optimization_type": "quality",
    "optimal_ratios": {...},
    "sinter_properties": {...},
    "unit_cost": 672.3,
    "objective_value": 0.025
  }
}
```

## 数据模型

### 原料数据模型
```csharp
public class MaterialData
{
    public string Name { get; set; }           // 原料名称
    public double Tfe { get; set; }            // TFe含量(%)
    public double Cao { get; set; }            // CaO含量(%)
    public double Sio2 { get; set; }           // SiO2含量(%)
    public double Mgo { get; set; }            // MgO含量(%)
    public double Al2o3 { get; set; }          // Al2O3含量(%)
    public double H2o { get; set; }            // 物理水(%)
    public double Ig { get; set; }             // 烧损率(%)
    public double PlannedPrice { get; set; }   // 计划单价(元/吨)
    public double MinRatio { get; set; }       // 最小配比(%)
    public double MaxRatio { get; set; }       // 最大配比(%)
}
```

### 优化参数模型
```csharp
public class OptimizationParameters
{
    public OptimizationType OptimizationType { get; set; }
    public double TfeMin { get; set; }
    public double TfeMax { get; set; }
    public double RoMin { get; set; }
    public double RoMax { get; set; }
    public RoCalculationType RoCalculationType { get; set; }
    public double CostTargetMax { get; set; }
    // ... 其他约束参数
}
```

### 优化结果模型
```csharp
public class OptimizationResult
{
    public bool Success { get; set; }
    public string OptimizationType { get; set; }
    public Dictionary<string, double> OptimalRatios { get; set; }
    public SinterProperties SinterProperties { get; set; }
    public double UnitCost { get; set; }
    public double ObjectiveValue { get; set; }
    public int Iterations { get; set; }
    public string Message { get; set; }
    public OptimizationResult AlternativeSolution { get; set; }
}
```

## 部署指南

### 开发环境部署

#### 1. 环境准备
```bash
# 安装.NET 6.0 SDK
winget install Microsoft.DotNet.SDK.6

# 安装Python 3.8+
winget install Python.Python.3.11

# 安装Git
winget install Git.Git
```

#### 2. 代码获取
```bash
git clone <repository-url>
cd sinter-optimization-system
```

#### 3. 后端部署
```bash
cd py
pip install -r requirements.txt
python optimization_service.py
```

#### 4. 前端部署
```bash
cd SinterOptimizationClient
dotnet restore
dotnet build
dotnet run
```

### 生产环境部署

#### 1. 服务器配置
- **操作系统**: Windows Server 2019/2022
- **内存**: 8GB+
- **CPU**: 4核心+
- **存储**: SSD 100GB+

#### 2. 服务化部署

**Python服务** (使用Gunicorn):
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 optimization_service:app
```

**C#应用** (发布为自包含应用):
```bash
dotnet publish -c Release -r win-x64 --self-contained
```

#### 3. 负载均衡
使用Nginx进行负载均衡和反向代理:
```nginx
upstream backend {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001;
}

server {
    listen 80;
    location /api/ {
        proxy_pass http://backend;
    }
}
```

## 开发指南

### 代码结构

#### C#项目结构
```
SinterOptimizationClient/
├── Models/                 # 数据模型
│   ├── MaterialData.cs
│   ├── OptimizationParameters.cs
│   └── OptimizationResult.cs
├── ViewModels/            # 视图模型
│   ├── MainViewModel.cs
│   ├── MaterialDataViewModel.cs
│   ├── OptimizationViewModel.cs
│   └── ResultViewModel.cs
├── Views/                 # 用户界面
│   ├── MaterialDataView.xaml
│   ├── OptimizationView.xaml
│   └── ResultView.xaml
├── Services/              # 服务层
│   ├── IOptimizationService.cs
│   ├── OptimizationService.cs
│   ├── IDataService.cs
│   └── DataService.cs
├── Converters/            # 数据转换器
│   └── ValueConverters.cs
└── Styles/               # 样式资源
    └── AppStyles.xaml
```

#### Python项目结构
```
py/
├── optimization_service.py    # Flask主服务
├── requirements.txt          # Python依赖
└── models/                   # 数据模型(可扩展)
```

### 扩展开发

#### 添加新的优化算法
1. 在`SinterOptimizer`类中添加新的优化方法
2. 实现相应的目标函数和约束条件
3. 在API接口中添加算法选择参数
4. 更新前端界面的算法选择选项

#### 添加新的约束条件
1. 在`OptimizationParameters`模型中添加新参数
2. 在`create_constraints`方法中实现约束逻辑
3. 更新前端界面的约束设置区域
4. 添加相应的数据验证

#### 自定义UI组件
1. 在`Styles/AppStyles.xaml`中定义新样式
2. 创建自定义UserControl
3. 实现相应的ViewModel
4. 添加数据绑定和命令处理

### 测试指南

#### 单元测试
```csharp
[Test]
public void TestMaterialDataValidation()
{
    var material = new MaterialData { Name = "Test", Tfe = 60.0 };
    Assert.IsTrue(material.IsValid());
}
```

#### 集成测试
```python
def test_optimization_api():
    response = requests.post('/api/solve', json=test_data)
    assert response.status_code == 200
    assert response.json()['success'] == True
```

#### 性能测试
- 测试大量原料数据的处理性能
- 验证复杂约束条件下的计算时间
- 监控内存使用情况

### 调试技巧

#### 前端调试
1. 使用Visual Studio的调试器
2. 在ViewModel中设置断点
3. 检查数据绑定和命令执行
4. 使用输出窗口查看日志

#### 后端调试
1. 在Flask应用中添加日志输出
2. 使用Python调试器(pdb)
3. 监控HTTP请求和响应
4. 检查算法收敛过程

---

**注意**: 本技术文档基于系统v1.0.0版本，随着系统更新可能会有变化。
