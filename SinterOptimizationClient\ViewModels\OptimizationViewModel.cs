using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using SinterOptimizationClient.Models;
using SinterOptimizationClient.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace SinterOptimizationClient.ViewModels
{
    public partial class OptimizationViewModel : ObservableObject
    {
        private readonly IOptimizationService _optimizationService;
        private readonly IDataService _dataService;
        private readonly IDialogService _dialogService;

        // 优化完成事件
        public event Action<OptimizationResult>? OptimizationCompleted;

        [ObservableProperty]
        private ObservableCollection<MaterialData> materials = new();

        [ObservableProperty]
        private OptimizationParameters parameters = new();

        [ObservableProperty]
        private bool isCalculating = false;

        [ObservableProperty]
        private string statusMessage = "就绪";

        [ObservableProperty]
        private double calculationProgress = 0;

        // 原料数据库补充参数
        [ObservableProperty]
        private ObservableCollection<MaterialSupplementData> materialSupplements = new();

        // 物料数据ViewModel
        public MaterialDataViewModel MaterialDataViewModel { get; private set; }

        public OptimizationViewModel(
            IOptimizationService optimizationService,
            IDataService dataService,
            IDialogService dialogService)
        {
            _optimizationService = optimizationService;
            _dataService = dataService;
            _dialogService = dialogService;

            // 创建物料数据ViewModel
            MaterialDataViewModel = new MaterialDataViewModel(dataService, dialogService);

            LoadMaterials();
            InitializeMaterialSupplements();
        }

        private async void LoadMaterials()
        {
            try
            {
                var materialList = await _dataService.LoadMaterialsAsync();
                Materials.Clear();
                foreach (var material in materialList)
                {
                    Materials.Add(material);
                }
                StatusMessage = $"已加载 {Materials.Count} 种原料";
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载原料数据失败: {ex.Message}";
                _dialogService.ShowError("加载错误", ex.Message);
            }
        }

        private void InitializeMaterialSupplements()
        {
            MaterialSupplements.Clear();
            foreach (var material in Materials)
            {
                MaterialSupplements.Add(new MaterialSupplementData
                {
                    MaterialName = material.Name,
                    PhysicalWater = material.H2o,
                    BurnLossRate = material.Ig,
                    UnitPrice = material.PlannedPrice
                });
            }
        }

        [RelayCommand]
        private async Task StartCalculation()
        {
            if (IsCalculating) return;

            try
            {
                // 验证参数
                if (!Parameters.IsValid())
                {
                    _dialogService.ShowError("参数错误", "请检查输入的参数是否正确");
                    return;
                }

                // 验证原料配比总和
                var totalRatio = Materials.Sum(m => m.WetRatio);
                if (Math.Abs(totalRatio - 100) > 0.1 && totalRatio > 0)
                {
                    _dialogService.ShowWarning("配比警告", $"当前湿配比总和为 {totalRatio:F1}%，建议调整至100%");
                }

                IsCalculating = true;
                StatusMessage = "正在进行优化计算...";
                CalculationProgress = 0;

                // 构建请求数据
                var requestData = BuildOptimizationRequest();

                // 调用优化服务
                var result = await _optimizationService.OptimizeAsync(requestData);

                if (result.Success)
                {
                    StatusMessage = "优化计算完成";
                    CalculationProgress = 100;

                    // 触发优化完成事件
                    OptimizationCompleted?.Invoke(result);

                    // 显示计算完成消息
                    _dialogService.ShowInformation("计算完成", "优化计算已完成，请切换到结果页面查看详细结果");
                }
                else
                {
                    StatusMessage = "优化计算失败";
                    _dialogService.ShowError("计算失败", result.ErrorMessage ?? "未知错误");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "计算过程中发生错误";
                _dialogService.ShowError("计算错误", $"计算过程中发生错误: {ex.Message}");
            }
            finally
            {
                IsCalculating = false;
            }
        }

        [RelayCommand]
        private void ResetParameters()
        {
            Parameters = new OptimizationParameters();
            StatusMessage = "参数已重置";
        }

        [RelayCommand]
        private void SetUniformRatioRange()
        {
            var input = _dialogService.ShowInputDialog("统一设置配比范围", "请输入最大配比值(%):", "30");
            if (double.TryParse(input, out var maxRatio) && maxRatio > 0 && maxRatio <= 100)
            {
                foreach (var material in Materials)
                {
                    material.MinRatio = 0;
                    material.MaxRatio = maxRatio;
                }
                StatusMessage = $"已统一设置配比范围为 0-{maxRatio}%";
            }
        }

        [RelayCommand]
        private void ValidateConstraints()
        {
            var errors = new List<string>();

            // 验证成分范围
            if (Parameters.TfeMin >= Parameters.TfeMax)
                errors.Add("TFe最小值应小于最大值");

            if (Parameters.Sio2Min >= Parameters.Sio2Max)
                errors.Add("SiO₂最小值应小于最大值");

            if (Parameters.CaoMin >= Parameters.CaoMax)
                errors.Add("CaO最小值应小于最大值");

            if (Parameters.MgoMin >= Parameters.MgoMax)
                errors.Add("MgO最小值应小于最大值");

            if (Parameters.RoMin >= Parameters.RoMax)
                errors.Add("碱度最小值应小于最大值");

            // 验证目标值是否在范围内
            if (Parameters.TfeTarget < Parameters.TfeMin || Parameters.TfeTarget > Parameters.TfeMax)
                errors.Add("TFe目标值应在设定范围内");

            if (Parameters.Sio2Target < Parameters.Sio2Min || Parameters.Sio2Target > Parameters.Sio2Max)
                errors.Add("SiO₂目标值应在设定范围内");

            if (Parameters.CaoTarget < Parameters.CaoMin || Parameters.CaoTarget > Parameters.CaoMax)
                errors.Add("CaO目标值应在设定范围内");

            if (Parameters.MgoTarget < Parameters.MgoMin || Parameters.MgoTarget > Parameters.MgoMax)
                errors.Add("MgO目标值应在设定范围内");

            if (Parameters.RoTarget < Parameters.RoMin || Parameters.RoTarget > Parameters.RoMax)
                errors.Add("碱度目标值应在设定范围内");

            // 验证配比约束
            var totalMinRatio = Materials.Sum(m => m.MinRatio);
            var totalMaxRatio = Materials.Sum(m => m.MaxRatio);

            if (totalMinRatio > 100)
                errors.Add($"原料最小配比总和({totalMinRatio:F1}%)超过100%");

            if (totalMaxRatio < 100)
                errors.Add($"原料最大配比总和({totalMaxRatio:F1}%)小于100%");

            if (errors.Any())
            {
                _dialogService.ShowError("约束验证失败", string.Join("\n", errors));
            }
            else
            {
                _dialogService.ShowInformation("约束验证", "所有约束条件验证通过");
            }
        }

        [RelayCommand]
        private void ConfirmConstraints()
        {
            // 验证约束条件
            ValidateConstraints();

            // 如果验证通过，显示确认消息
            var message = $"约束条件已确认:\n" +
                         $"TFe: {Parameters.TfeMin:F1}% ~ {Parameters.TfeMax:F1}% (目标: {Parameters.TfeTarget:F1}%)\n" +
                         $"SiO₂: {Parameters.Sio2Min:F1}% ~ {Parameters.Sio2Max:F1}% (目标: {Parameters.Sio2Target:F1}%)\n" +
                         $"CaO: {Parameters.CaoMin:F1}% ~ {Parameters.CaoMax:F1}% (目标: {Parameters.CaoTarget:F1}%)\n" +
                         $"MgO: {Parameters.MgoMin:F1}% ~ {Parameters.MgoMax:F1}% (目标: {Parameters.MgoTarget:F1}%)\n" +
                         $"碱度: {Parameters.RoMin:F2} ~ {Parameters.RoMax:F2} (目标: {Parameters.RoTarget:F2})\n" +
                         $"成本上限: {Parameters.CostTargetMax:F0} 元/吨";

            _dialogService.ShowInformation("约束条件确认", message);
            StatusMessage = "约束条件已确认";
        }

        [RelayCommand]
        private void ConfirmOptimizationTarget()
        {
            var optimizationTypeText = Parameters.OptimizationType == OptimizationType.CostOptimal ? "成本最优" : "质量最优";

            var message = $"优化目标已确认:\n" +
                         $"优化类型: {optimizationTypeText}\n" +
                         $"算法类型: {Parameters.AlgorithmType}\n" +
                         $"计划日产: {Parameters.PlannedDailyOutput:F0} 吨\n" +
                         $"金属收得率: {Parameters.MetalRecoveryRate:F0}%\n" +
                         $"出矿率: {Parameters.SinterYield:F0}%\n" +
                         $"作业率: {Parameters.OperationRate:F0}%";

            _dialogService.ShowInformation("优化目标确认", message);
            StatusMessage = $"优化目标已确认: {optimizationTypeText}";
        }

        [RelayCommand]
        private void ShowMaterialSelection()
        {
            try
            {
                // 创建物料选择对话框的ViewModel
                var dialogViewModel = new MaterialSelectionDialogViewModel(Materials);

                // 创建并显示对话框
                var dialog = new Views.MaterialSelectionDialog(dialogViewModel);
                dialog.Owner = Application.Current.MainWindow;

                var result = dialog.ShowDialog();

                if (result == true)
                {
                    // 用户点击了"应用选择"，更新物料列表
                    var selectedMaterials = dialogViewModel.GetSelectedMaterials(Materials);

                    // 更新Materials集合，将未选中的物料的MaxRatio设为0
                    var selectedNames = selectedMaterials.Select(m => m.Name).ToHashSet();

                    foreach (var material in Materials)
                    {
                        if (!selectedNames.Contains(material.Name))
                        {
                            material.MaxRatio = 0; // 未选中的物料设为不参与计算
                        }
                        else if (material.MaxRatio == 0)
                        {
                            material.MaxRatio = 30; // 重新选中的物料恢复默认最大配比
                        }
                    }

                    StatusMessage = $"物料选择已更新，共选择 {selectedNames.Count} 种物料";
                    _dialogService.ShowInformation("选择完成", $"已选择 {selectedNames.Count} 种物料参与优化计算");
                }
                else
                {
                    StatusMessage = "物料选择已取消";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "物料选择失败";
                _dialogService.ShowError("错误", $"打开物料选择对话框时发生错误: {ex.Message}");
            }
        }

        private OptimizationRequest BuildOptimizationRequest()
        {
            return new OptimizationRequest
            {
                RawMaterials = Materials.Select(m => new RawMaterialData
                {
                    Name = m.Name,
                    Tfe = m.Tfe,
                    Cao = m.Cao,
                    Sio2 = m.Sio2,
                    Mgo = m.Mgo,
                    Al2o3 = m.Al2o3,
                    H2o = m.H2o,
                    Ig = m.Ig,
                    Price = m.PlannedPrice,
                    MinRatio = m.MinRatio,
                    MaxRatio = m.MaxRatio
                }).ToList(),

                Target = new OptimizationTarget
                {
                    TfeTarget = Parameters.TfeTarget,
                    RoTarget = Parameters.RoTarget,
                    MgoTarget = Parameters.MgoTarget,
                    Al2o3Target = Parameters.Al2o3Target
                },

                Constraints = new OptimizationConstraints
                {
                    TfeRange = (Parameters.TfeMin, Parameters.TfeMax),
                    RoRange = (Parameters.RoMin, Parameters.RoMax),
                    MgoRange = (Parameters.MgoMin, Parameters.MgoMax),
                    Al2o3Range = (0, Parameters.Al2o3Max),
                    CostRange = (0, Parameters.CostTargetMax)
                },

                OptimizeType = Parameters.OptimizationType == OptimizationType.CostOptimal ? "cost" : "quality",
                MultiSolution = true
            };
        }
    }

    /// <summary>
    /// 原料补充数据
    /// </summary>
    public partial class MaterialSupplementData : ObservableObject
    {
        [ObservableProperty]
        private string materialName = string.Empty;

        [ObservableProperty]
        private double physicalWater;

        [ObservableProperty]
        private double burnLossRate;

        [ObservableProperty]
        private double unitPrice;
    }
}
