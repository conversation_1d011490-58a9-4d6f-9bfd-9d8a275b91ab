<Window x:Class="SinterOptimizationClient.Views.MaterialSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="物料选择" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- 物料类别颜色样式 -->
        <Style x:Key="MaterialCategoryStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="4,2"/>
            <Setter Property="Margin" Value="2,0"/>
        </Style>
        
        <!-- 精粉类别样式 -->
        <Style x:Key="JingFenStyle" TargetType="Border" BasedOn="{StaticResource MaterialCategoryStyle}">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <!-- 粗粉类别样式 -->
        <Style x:Key="CuFenStyle" TargetType="Border" BasedOn="{StaticResource MaterialCategoryStyle}">
            <Setter Property="Background" Value="#FFF3E0"/>
            <Setter Property="BorderBrush" Value="#FF9800"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <!-- 返矿类别样式 -->
        <Style x:Key="FanKuangStyle" TargetType="Border" BasedOn="{StaticResource MaterialCategoryStyle}">
            <Setter Property="Background" Value="#E8F5E8"/>
            <Setter Property="BorderBrush" Value="#4CAF50"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <!-- 渣料类别样式 -->
        <Style x:Key="ZhaLiaoStyle" TargetType="Border" BasedOn="{StaticResource MaterialCategoryStyle}">
            <Setter Property="Background" Value="#FCE4EC"/>
            <Setter Property="BorderBrush" Value="#E91E63"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <!-- 添加剂类别样式 -->
        <Style x:Key="TianJiaJiStyle" TargetType="Border" BasedOn="{StaticResource MaterialCategoryStyle}">
            <Setter Property="Background" Value="#F3E5F5"/>
            <Setter Property="BorderBrush" Value="#9C27B0"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <!-- 回收类别样式 -->
        <Style x:Key="HuiShouStyle" TargetType="Border" BasedOn="{StaticResource MaterialCategoryStyle}">
            <Setter Property="Background" Value="#E0F2F1"/>
            <Setter Property="BorderBrush" Value="#009688"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <!-- 铁皮类别样式 -->
        <Style x:Key="TiePiStyle" TargetType="Border" BasedOn="{StaticResource MaterialCategoryStyle}">
            <Setter Property="Background" Value="#EFEBE9"/>
            <Setter Property="BorderBrush" Value="#795548"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <!-- 焦粉类别样式 -->
        <Style x:Key="JiaoFenStyle" TargetType="Border" BasedOn="{StaticResource MaterialCategoryStyle}">
            <Setter Property="Background" Value="#FAFAFA"/>
            <Setter Property="BorderBrush" Value="#616161"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和说明 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,15">
            <TextBlock Text="物料选择" FontSize="18" FontWeight="Bold" Margin="0,0,0,8"/>
            <TextBlock Text="请选择参与优化计算的物料，不同类别用不同颜色标识" FontSize="12" Foreground="#666" Margin="0,0,0,10"/>
            
            <!-- 类别图例 -->
            <WrapPanel Margin="0,0,0,10">
                <Border Style="{StaticResource JingFenStyle}">
                    <TextBlock Text="精粉" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource CuFenStyle}">
                    <TextBlock Text="粗粉" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource FanKuangStyle}">
                    <TextBlock Text="返矿" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource ZhaLiaoStyle}">
                    <TextBlock Text="渣料" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource TianJiaJiStyle}">
                    <TextBlock Text="添加剂" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource HuiShouStyle}">
                    <TextBlock Text="回收" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource TiePiStyle}">
                    <TextBlock Text="铁皮" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource JiaoFenStyle}">
                    <TextBlock Text="焦粉" FontSize="10"/>
                </Border>
            </WrapPanel>
        </StackPanel>

        <!-- 物料列表 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧列 -->
            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="左侧物料列表" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                <ScrollViewer MaxHeight="400" VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding LeftColumnMaterials}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,2" Padding="8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}" Margin="0,0,8,0"/>
                                        
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="12"/>
                                            <TextBlock Text="{Binding PriceText}" FontSize="10" Foreground="#666"/>
                                        </StackPanel>
                                        
                                        <Border Grid.Column="2" Margin="5,0" CornerRadius="3" Padding="4,2" BorderThickness="1">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Category}" Value="精粉">
                                                            <Setter Property="Background" Value="#E3F2FD"/>
                                                            <Setter Property="BorderBrush" Value="#2196F3"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="粗粉">
                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                            <Setter Property="BorderBrush" Value="#FF9800"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="返矿">
                                                            <Setter Property="Background" Value="#E8F5E8"/>
                                                            <Setter Property="BorderBrush" Value="#4CAF50"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="渣料">
                                                            <Setter Property="Background" Value="#FCE4EC"/>
                                                            <Setter Property="BorderBrush" Value="#E91E63"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="添加剂">
                                                            <Setter Property="Background" Value="#F3E5F5"/>
                                                            <Setter Property="BorderBrush" Value="#9C27B0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="回收">
                                                            <Setter Property="Background" Value="#E0F2F1"/>
                                                            <Setter Property="BorderBrush" Value="#009688"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="铁皮">
                                                            <Setter Property="Background" Value="#EFEBE9"/>
                                                            <Setter Property="BorderBrush" Value="#795548"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="焦粉">
                                                            <Setter Property="Background" Value="#FAFAFA"/>
                                                            <Setter Property="BorderBrush" Value="#616161"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding Category}" FontSize="9"/>
                                        </Border>
                                        
                                        <TextBlock Grid.Column="3" Text="{Binding SelectionStatus}" FontSize="10" 
                                                   Foreground="{Binding StatusColor}" Margin="5,0,0,0"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </StackPanel>

            <!-- 右侧列 -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <TextBlock Text="右侧物料列表" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                <ScrollViewer MaxHeight="400" VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding RightColumnMaterials}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,2" Padding="8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}" Margin="0,0,8,0"/>
                                        
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="12"/>
                                            <TextBlock Text="{Binding PriceText}" FontSize="10" Foreground="#666"/>
                                        </StackPanel>
                                        
                                        <Border Grid.Column="2" Margin="5,0" CornerRadius="3" Padding="4,2" BorderThickness="1">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Category}" Value="精粉">
                                                            <Setter Property="Background" Value="#E3F2FD"/>
                                                            <Setter Property="BorderBrush" Value="#2196F3"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="粗粉">
                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                            <Setter Property="BorderBrush" Value="#FF9800"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="返矿">
                                                            <Setter Property="Background" Value="#E8F5E8"/>
                                                            <Setter Property="BorderBrush" Value="#4CAF50"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="渣料">
                                                            <Setter Property="Background" Value="#FCE4EC"/>
                                                            <Setter Property="BorderBrush" Value="#E91E63"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="添加剂">
                                                            <Setter Property="Background" Value="#F3E5F5"/>
                                                            <Setter Property="BorderBrush" Value="#9C27B0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="回收">
                                                            <Setter Property="Background" Value="#E0F2F1"/>
                                                            <Setter Property="BorderBrush" Value="#009688"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="铁皮">
                                                            <Setter Property="Background" Value="#EFEBE9"/>
                                                            <Setter Property="BorderBrush" Value="#795548"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Category}" Value="焦粉">
                                                            <Setter Property="Background" Value="#FAFAFA"/>
                                                            <Setter Property="BorderBrush" Value="#616161"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding Category}" FontSize="9"/>
                                        </Border>
                                        
                                        <TextBlock Grid.Column="3" Text="{Binding SelectionStatus}" FontSize="10" 
                                                   Foreground="{Binding StatusColor}" Margin="5,0,0,0"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </StackPanel>
        </Grid>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="全选" Width="60" Height="30" Margin="0,0,10,0" Click="SelectAll_Click"/>
            <Button Content="全不选" Width="60" Height="30" Margin="0,0,10,0" Click="SelectNone_Click"/>
            <Button Content="应用选择" Width="80" Height="30" Margin="0,0,10,0" 
                    Background="#4CAF50" Foreground="White" Click="Apply_Click"/>
            <Button Content="取消" Width="60" Height="30" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
