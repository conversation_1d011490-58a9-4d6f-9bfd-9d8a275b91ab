#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本 - 测试C#客户端与Python后端的通信
"""

import requests
import json
import time
import subprocess
import sys
import os

def test_flask_service():
    """测试Flask服务是否正常运行"""
    print("🔍 测试Flask服务连接...")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Flask服务运行正常")
            return True
        else:
            print(f"❌ Flask服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Flask服务，请确保服务已启动")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_optimization_api():
    """测试优化API接口"""
    print("\n🧪 测试优化API接口...")
    
    # 构建测试数据
    test_data = {
        "raw_materials": [
            {
                "name": "碱性精粉",
                "tfe": 63.76,
                "cao": 1.94,
                "sio2": 4.95,
                "mgo": 1.85,
                "al2o3": 0.60,
                "h2o": 8.20,
                "ig": 1.23,
                "price": 752.21,
                "min_ratio": 0,
                "max_ratio": 30
            },
            {
                "name": "酸性精粉",
                "tfe": 64.89,
                "cao": 0.70,
                "sio2": 6.32,
                "mgo": 0.92,
                "al2o3": 0.72,
                "h2o": 9.90,
                "ig": -0.05,
                "price": 752.21,
                "min_ratio": 0,
                "max_ratio": 30
            },
            {
                "name": "印粉海娜",
                "tfe": 63.66,
                "cao": 0.10,
                "sio2": 4.01,
                "mgo": 0.24,
                "al2o3": 2.42,
                "h2o": 6.70,
                "ig": 1.60,
                "price": 832.98,
                "min_ratio": 8,
                "max_ratio": 25
            },
            {
                "name": "生石灰",
                "tfe": 0.00,
                "cao": 71.74,
                "sio2": 3.52,
                "mgo": 2.28,
                "al2o3": 1.19,
                "h2o": 7.00,
                "ig": 16.33,
                "price": 219.00,
                "min_ratio": 3,
                "max_ratio": 8
            },
            {
                "name": "焦粉",
                "tfe": 0.19,
                "cao": 0.37,
                "sio2": 8.82,
                "mgo": 0.22,
                "al2o3": 3.31,
                "h2o": 13.15,
                "ig": 79.40,
                "price": 520.00,
                "min_ratio": 3,
                "max_ratio": 6
            }
        ],
        "target": {
            "tfe_target": 55.0,
            "ro_target": 1.90,
            "mgo_target": 2.39,
            "al2o3_target": 1.89
        },
        "constraints": {
            "tfe_range": [54, 56],
            "ro_range": [1.88, 2.02],
            "mgo_range": [1.8, 3.0],
            "al2o3_range": [1.5, 2.5],
            "cost_range": [600, 680]
        },
        "optimize_type": "cost",
        "multi_solution": True
    }
    
    try:
        print("📤 发送优化请求...")
        response = requests.post(
            "http://localhost:5000/api/solve",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 优化计算成功!")
                print(f"   优化类型: {result.get('optimization_type')}")
                print(f"   单位成本: {result.get('unit_cost', 0):.2f} 元/吨")
                print(f"   迭代次数: {result.get('iterations', 0)}")
                
                # 显示最优配比
                ratios = result.get('optimal_ratios', {})
                print("   最优配比:")
                for material, ratio in ratios.items():
                    if ratio > 0.01:
                        print(f"     {material}: {ratio:.2f}%")
                
                # 显示烧结矿性质
                props = result.get('sinter_properties', {})
                print("   烧结矿性质:")
                print(f"     TFe: {props.get('tfe', 0):.2f}%")
                print(f"     碱度R: {props.get('r', 0):.3f}")
                print(f"     MgO: {props.get('mgo', 0):.2f}%")
                print(f"     Al₂O₃: {props.get('al2o3', 0):.2f}%")
                
                # 检查备选方案
                if 'alternative_solution' in result:
                    print("   备选方案: 已生成")
                
                return True
            else:
                print(f"❌ 优化计算失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，优化计算可能需要更长时间")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def start_flask_service():
    """启动Flask服务"""
    print("🚀 启动Flask服务...")
    
    try:
        # 检查Python文件是否存在
        flask_file = "py/optimization_service.py"
        if not os.path.exists(flask_file):
            print(f"❌ Flask服务文件不存在: {flask_file}")
            return None
        
        # 启动Flask服务
        process = subprocess.Popen(
            [sys.executable, flask_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd="."
        )
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ Flask服务启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Flask服务启动失败")
            print(f"   stdout: {stdout.decode()}")
            print(f"   stderr: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ 启动Flask服务失败: {e}")
        return None

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔥 烧结矿成分及指标计算系统 - 集成测试")
    print("=" * 60)
    
    # 首先尝试连接现有服务
    if not test_flask_service():
        print("\n🔄 尝试启动Flask服务...")
        process = start_flask_service()
        
        if process is None:
            print("\n❌ 无法启动Flask服务，测试终止")
            return False
        
        # 再次测试连接
        time.sleep(2)
        if not test_flask_service():
            print("\n❌ Flask服务启动后仍无法连接")
            process.terminate()
            return False
    
    # 测试优化API
    success = test_optimization_api()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 集成测试通过! 系统可以正常工作")
        print("💡 现在可以启动C#客户端进行完整测试")
    else:
        print("❌ 集成测试失败，请检查系统配置")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
