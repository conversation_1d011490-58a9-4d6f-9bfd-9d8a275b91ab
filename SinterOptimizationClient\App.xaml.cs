using System;
using System.Windows;

namespace SinterOptimizationClient
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                var mainWindow = new MainWindow();
                mainWindow.Show();
                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动错误: {ex.Message}\n\n详细信息:\n{ex}", "应用程序启动失败", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }
    }
}
