using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationClient.Models
{
    /// <summary>
    /// 原料数据模型 - 对应技术文档中的物料信息表结构
    /// </summary>
    public partial class MaterialData : ObservableValidator
    {
        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "TFe含量应在0-100%之间")]
        private double tfe;

        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "CaO含量应在0-100%之间")]
        private double cao;

        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "SiO2含量应在0-100%之间")]
        private double sio2;

        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "MgO含量应在0-100%之间")]
        private double mgo;

        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "Al2O3含量应在0-100%之间")]
        private double al2o3;

        [ObservableProperty]
        private double cs;

        [ObservableProperty]
        private double tio2;

        [ObservableProperty]
        private double ig;

        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "水分应在0-100%之间")]
        private double h2o;

        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "湿配比应在0-100%之间")]
        private double wetRatio;

        [ObservableProperty]
        private double dryRatio;

        [ObservableProperty]
        private double burnAmount;

        [ObservableProperty]
        private double burnTfe;

        [ObservableProperty]
        private double burnCao;

        [ObservableProperty]
        private double burnSio2;

        [ObservableProperty]
        private double burnMgo;

        [ObservableProperty]
        private double burnAl2o3;

        [ObservableProperty]
        private double tonConsumption;

        [ObservableProperty]
        [Range(0, double.MaxValue, ErrorMessage = "单价不能为负数")]
        private double plannedPrice;

        [ObservableProperty]
        private double unitCost;

        // 约束条件
        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "最小配比应在0-100%之间")]
        private double minRatio;

        [ObservableProperty]
        [Range(0, 100, ErrorMessage = "最大配比应在0-100%之间")]
        private double maxRatio = 100;

        // 计算属性
        public double CS => Sio2 > 0 ? Cao / Sio2 : 0;

        // 验证方法
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   Tfe >= 0 && Tfe <= 100 &&
                   Cao >= 0 && Cao <= 100 &&
                   Sio2 >= 0 && Sio2 <= 100 &&
                   Mgo >= 0 && Mgo <= 100 &&
                   Al2o3 >= 0 && Al2o3 <= 100 &&
                   H2o >= 0 && H2o <= 100 &&
                   WetRatio >= 0 && WetRatio <= 100 &&
                   PlannedPrice >= 0 &&
                   MinRatio >= 0 && MinRatio <= 100 &&
                   MaxRatio >= 0 && MaxRatio <= 100 &&
                   MinRatio <= MaxRatio;
        }

        // 创建默认原料数据
        public static List<MaterialData> CreateDefaultMaterials()
        {
            return new List<MaterialData>
            {
                new() { Name = "碱性精粉", Tfe = 63.76, Cao = 1.94, Sio2 = 4.95, Mgo = 1.85, Al2o3 = 0.60, Tio2 = 0.01, Ig = 1.23, H2o = 8.20, PlannedPrice = 752.21, MinRatio = 0, MaxRatio = 30 },
                new() { Name = "酸性精粉", Tfe = 64.89, Cao = 0.70, Sio2 = 6.32, Mgo = 0.92, Al2o3 = 0.72, Tio2 = 0.09, Ig = -0.05, H2o = 9.90, PlannedPrice = 752.21, MinRatio = 0, MaxRatio = 30 },
                new() { Name = "海瑞", Tfe = 58.07, Cao = 0.10, Sio2 = 6.21, Mgo = 0.28, Al2o3 = 2.52, Tio2 = 0.07, Ig = 9.07, H2o = 6.00, PlannedPrice = 822.98, MinRatio = 0, MaxRatio = 25 },
                new() { Name = "印粉海娜", Tfe = 63.66, Cao = 0.10, Sio2 = 4.01, Mgo = 0.24, Al2o3 = 2.42, Tio2 = 0.06, Ig = 1.60, H2o = 6.70, WetRatio = 20.0, PlannedPrice = 832.98, MinRatio = 8, MaxRatio = 25 },
                new() { Name = "巴西粗粉", Tfe = 64.64, Cao = 0.20, Sio2 = 4.69, Mgo = 0.11, Al2o3 = 0.73, Tio2 = 0.02, Ig = 1.33, H2o = 6.70, PlannedPrice = 1473.05, MinRatio = 0, MaxRatio = 20 },
                new() { Name = "俄罗斯精粉", Tfe = 62.95, Cao = 1.71, Sio2 = 4.61, Mgo = 3.70, Al2o3 = 2.29, Tio2 = 0.22, Ig = -0.35, H2o = 10.00, WetRatio = 14.0, PlannedPrice = 772.21, MinRatio = 5, MaxRatio = 20 },
                new() { Name = "高炉返矿", Tfe = 55.54, Cao = 10.60, Sio2 = 5.59, Mgo = 2.34, Al2o3 = 2.09, Tio2 = 0.00, Ig = 1.73, H2o = 0.50, WetRatio = 20.0, PlannedPrice = 550.00, MinRatio = 10, MaxRatio = 25 },
                new() { Name = "回收料", Tfe = 56.16, Cao = 6.56, Sio2 = 6.31, Mgo = 2.39, Al2o3 = 2.51, Tio2 = 0.20, Ig = 1.74, H2o = 10.73, WetRatio = 8.0, PlannedPrice = 100.00, MinRatio = 5, MaxRatio = 15 },
                new() { Name = "钢渣", Tfe = 26.46, Cao = 28.15, Sio2 = 15.43, Mgo = 2.79, Al2o3 = 2.53, Tio2 = 0.00, Ig = 12.05, H2o = 7.60, WetRatio = 4.0, PlannedPrice = 550.00, MinRatio = 2, MaxRatio = 8 },
                new() { Name = "氧化铁皮", Tfe = 69.73, Cao = 0.50, Sio2 = 1.50, Mgo = 0.00, Al2o3 = 2.88, Tio2 = 0.00, Ig = -1.52, H2o = 5.90, PlannedPrice = 750.00, MinRatio = 0, MaxRatio = 10 },
                new() { Name = "生石灰", Tfe = 0.00, Cao = 71.74, Sio2 = 3.52, Mgo = 2.28, Al2o3 = 1.19, Tio2 = 0.00, Ig = 16.33, H2o = 7.00, WetRatio = 6.6, PlannedPrice = 219.00, MinRatio = 3, MaxRatio = 8 },
                new() { Name = "轻烧白云石", Tfe = 0.00, Cao = 42.67, Sio2 = 5.31, Mgo = 26.12, Al2o3 = 0.10, Tio2 = 0.00, Ig = 19.73, H2o = 1.50, WetRatio = 3.0, PlannedPrice = 183.76, MinRatio = 1, MaxRatio = 5 },
                new() { Name = "焦粉", Tfe = 0.19, Cao = 0.37, Sio2 = 8.82, Mgo = 0.22, Al2o3 = 3.31, Tio2 = 0.00, Ig = 79.40, H2o = 13.15, WetRatio = 5.0, PlannedPrice = 520.00, MinRatio = 3, MaxRatio = 6 },
                new() { Name = "澳粉纵横", Tfe = 60.80, Cao = 0.10, Sio2 = 4.35, Mgo = 0.20, Al2o3 = 2.30, Tio2 = 0.06, Ig = 6.89, H2o = 8.30, WetRatio = 20.0, PlannedPrice = 832.98, MinRatio = 8, MaxRatio = 20 }
            };
        }
    }
}
