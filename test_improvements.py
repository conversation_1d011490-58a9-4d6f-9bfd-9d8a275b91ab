#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烧结矿成分及指标计算系统改进验证测试脚本
测试UI和功能改进的效果
"""

import requests
import json
import time
import sys
import os

def test_backend_optimization_types():
    """测试后端优化算法区分功能"""
    print("🔍 测试后端优化算法区分功能...")
    
    # 测试数据
    test_data = {
        "raw_materials": [
            {"name": "碱性精粉", "tfe": 63.76, "cao": 1.94, "sio2": 4.95, "mgo": 1.85, "al2o3": 0.60, "h2o": 8.20, "ig": 1.23, "price": 752.21, "min_ratio": 0, "max_ratio": 30},
            {"name": "酸性精粉", "tfe": 64.89, "cao": 0.70, "sio2": 6.32, "mgo": 0.92, "al2o3": 0.72, "h2o": 9.90, "ig": -0.05, "price": 752.21, "min_ratio": 0, "max_ratio": 30},
            {"name": "海瑞", "tfe": 58.07, "cao": 0.10, "sio2": 6.21, "mgo": 0.28, "al2o3": 2.52, "h2o": 6.00, "ig": 9.07, "price": 822.98, "min_ratio": 0, "max_ratio": 25}
        ],
        "target": {
            "tfe_target": 55.0,
            "ro_target": 1.90,
            "mgo_target": 2.0,
            "al2o3_target": 1.8
        },
        "constraints": {
            "tfe_range": [54.0, 56.0],
            "ro_range": [1.8, 2.1],
            "mgo_range": [1.8, 2.2],
            "al2o3_range": [1.5, 2.0],
            "cost_range": [600, 680]
        },
        "multi_solution": True
    }
    
    try:
        # 测试成本最优
        print("\n📊 测试成本最优算法...")
        cost_data = test_data.copy()
        cost_data["optimize_type"] = "cost"
        
        response = requests.post("http://localhost:5000/api/solve", 
                               json=cost_data, 
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 成本最优算法测试成功")
                print(f"   优化类型: {result.get('optimization_type_text', 'N/A')}")
                print(f"   单位成本: {result.get('unit_cost', 0):.2f} 元/吨")
                print(f"   算法策略: {result.get('algorithm_strategy', 'N/A')}")
                
                # 检查是否有备选方案
                if 'alternative_solution' in result:
                    alt = result['alternative_solution']
                    print(f"   备选方案: {alt.get('optimization_type_text', 'N/A')}")
                    print(f"   备选成本: {alt.get('unit_cost', 0):.2f} 元/吨")
            else:
                print(f"❌ 成本最优算法测试失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
        # 测试质量最优
        print("\n🎯 测试质量最优算法...")
        quality_data = test_data.copy()
        quality_data["optimize_type"] = "quality"
        
        response = requests.post("http://localhost:5000/api/solve", 
                               json=quality_data, 
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 质量最优算法测试成功")
                print(f"   优化类型: {result.get('optimization_type_text', 'N/A')}")
                print(f"   单位成本: {result.get('unit_cost', 0):.2f} 元/吨")
                print(f"   算法策略: {result.get('algorithm_strategy', 'N/A')}")
                
                # 检查是否有备选方案
                if 'alternative_solution' in result:
                    alt = result['alternative_solution']
                    print(f"   备选方案: {alt.get('optimization_type_text', 'N/A')}")
                    print(f"   备选成本: {alt.get('unit_cost', 0):.2f} 元/吨")
            else:
                print(f"❌ 质量最优算法测试失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保Python服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    
    return True

def test_flask_service():
    """测试Flask服务是否正常运行"""
    print("🔍 测试Flask服务连接...")
    
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Flask服务运行正常")
            return True
        else:
            print(f"❌ Flask服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Flask服务，请确保Python后端正在运行")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def print_improvement_summary():
    """打印改进总结"""
    print("\n" + "="*80)
    print("🎉 烧结矿成分及指标计算系统改进完成总结")
    print("="*80)
    
    improvements = [
        "✅ UI布局调整:",
        "   - 移除了原料数据库补充参数区域，释放空间",
        "   - 物料信息表高度从25%增加到70%，显示更多数据",
        "   - 重新分配区域宽度：约束条件区域是优化目标区域的2倍",
        "",
        "✅ 功能增强:",
        "   - 约束条件设置提供具体的目标值输入框",
        "   - 添加了约束条件确认按钮",
        "   - 添加了配料优化目标确认按钮",
        "   - 优化结果页面清晰区分成本最优和质量最优结果",
        "",
        "✅ 算法逻辑改进:",
        "   - 质量最优：严格按约束条件，成本上限680元/吨",
        "   - 成本最优：适当放宽约束条件，以降低成本为主",
        "   - 优化结果包含清晰的算法策略说明",
        "",
        "✅ 样式提升:",
        "   - 增强了工业风设计的视觉效果",
        "   - 改进了按钮样式，增加了阴影和悬停效果",
        "   - 优化了输入框样式，增加了焦点效果",
        "   - 添加了成功、警告等状态颜色",
        "",
        "✅ 用户体验改进:",
        "   - 添加了图标和颜色来区分优化类型",
        "   - 增强了交互反馈和状态提示",
        "   - 改进了数据验证和错误提示"
    ]
    
    for item in improvements:
        print(item)
    
    print("\n" + "="*80)
    print("🚀 系统已准备就绪，可以开始使用改进后的功能！")
    print("="*80)

def main():
    """主测试函数"""
    print("🔥 烧结矿成分及指标计算系统改进验证测试")
    print("="*60)
    
    # 测试后端服务
    if not test_flask_service():
        print("\n⚠️  请先启动Python后端服务:")
        print("   cd py")
        print("   python optimization_service.py")
        return
    
    # 测试优化算法区分
    if test_backend_optimization_types():
        print("\n✅ 所有后端功能测试通过！")
    else:
        print("\n❌ 部分后端功能测试失败")
    
    # 打印改进总结
    print_improvement_summary()
    
    print("\n📋 下一步操作建议:")
    print("1. 启动C#客户端应用程序")
    print("2. 测试新的约束条件输入功能")
    print("3. 验证优化目标确认功能")
    print("4. 检查优化结果页面的类型区分显示")
    print("5. 体验改进后的UI样式和交互效果")

if __name__ == "__main__":
    main()
