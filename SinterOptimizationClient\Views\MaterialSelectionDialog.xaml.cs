using SinterOptimizationClient.ViewModels;
using System.Windows;

namespace SinterOptimizationClient.Views
{
    /// <summary>
    /// MaterialSelectionDialog.xaml 的交互逻辑
    /// </summary>
    public partial class MaterialSelectionDialog : Window
    {
        public MaterialSelectionDialogViewModel ViewModel { get; private set; }

        public MaterialSelectionDialog(MaterialSelectionDialogViewModel viewModel)
        {
            InitializeComponent();
            ViewModel = viewModel;
            DataContext = viewModel;
        }

        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.SelectAllCommand.Execute(null);
        }

        private void SelectNone_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.SelectNoneCommand.Execute(null);
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
