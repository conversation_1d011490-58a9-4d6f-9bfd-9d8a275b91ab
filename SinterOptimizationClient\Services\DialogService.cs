using System.Windows;

namespace SinterOptimizationClient.Services
{
    public class DialogService : IDialogService
    {
        public void ShowInformation(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public void ShowWarning(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        public void ShowError(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        public bool ShowConfirmation(string title, string message)
        {
            var result = MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
            return result == MessageBoxResult.Yes;
        }

        public string? ShowInputDialog(string title, string prompt, string defaultValue = "")
        {
            // 这里可以实现一个自定义的输入对话框
            // 暂时使用简单的实现
            return Microsoft.VisualBasic.Interaction.InputBox(prompt, title, defaultValue);
        }
    }
}
