using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using SinterOptimizationClient.Models;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Media;

namespace SinterOptimizationClient.ViewModels
{
    /// <summary>
    /// 物料选择对话框的ViewModel
    /// </summary>
    public partial class MaterialSelectionDialogViewModel : ObservableObject
    {
        [ObservableProperty]
        private ObservableCollection<MaterialSelectionItem> leftColumnMaterials = new();

        [ObservableProperty]
        private ObservableCollection<MaterialSelectionItem> rightColumnMaterials = new();

        public MaterialSelectionDialogViewModel(ObservableCollection<MaterialData> materials)
        {
            InitializeMaterialLists(materials);
        }

        private void InitializeMaterialLists(ObservableCollection<MaterialData> materials)
        {
            // 根据需求文档创建物料列表
            var leftMaterials = new[]
            {
                new MaterialSelectionItem("碱性精粉", "精粉", false, 752.21),
                new MaterialSelectionItem("海瑞", "精粉", false, 822.98),
                new MaterialSelectionItem("巴西粗粉", "粗粉", false, 1473.05),
                new MaterialSelectionItem("高炉返矿", "返矿", true, 550.00),
                new MaterialSelectionItem("钢渣", "渣料", true, 550.00),
                new MaterialSelectionItem("生石灰", "添加剂", true, 219.00),
                new MaterialSelectionItem("焦粉", "焦粉", true, 520.00)
            };

            var rightMaterials = new[]
            {
                new MaterialSelectionItem("酸性精粉", "精粉", false, 752.21),
                new MaterialSelectionItem("印粉海娜", "精粉", true, 822.98),
                new MaterialSelectionItem("俄罗斯精粉", "精粉", true, 772.21),
                new MaterialSelectionItem("回收料", "回收", true, 100.00),
                new MaterialSelectionItem("氧化铁皮", "铁皮", false, 0),
                new MaterialSelectionItem("轻烧白云石", "添加剂", true, 183.76),
                new MaterialSelectionItem("澳粉纵横", "精粉", true, 832.98)
            };

            LeftColumnMaterials.Clear();
            RightColumnMaterials.Clear();

            foreach (var item in leftMaterials)
            {
                LeftColumnMaterials.Add(item);
            }

            foreach (var item in rightMaterials)
            {
                RightColumnMaterials.Add(item);
            }
        }

        [RelayCommand]
        private void SelectAll()
        {
            foreach (var item in LeftColumnMaterials)
            {
                item.IsSelected = true;
            }
            foreach (var item in RightColumnMaterials)
            {
                item.IsSelected = true;
            }
        }

        [RelayCommand]
        private void SelectNone()
        {
            foreach (var item in LeftColumnMaterials)
            {
                item.IsSelected = false;
            }
            foreach (var item in RightColumnMaterials)
            {
                item.IsSelected = false;
            }
        }

        public ObservableCollection<MaterialData> GetSelectedMaterials(ObservableCollection<MaterialData> originalMaterials)
        {
            var selectedNames = LeftColumnMaterials.Concat(RightColumnMaterials)
                .Where(item => item.IsSelected)
                .Select(item => item.Name)
                .ToHashSet();

            var selectedMaterials = new ObservableCollection<MaterialData>();
            foreach (var material in originalMaterials)
            {
                if (selectedNames.Contains(material.Name))
                {
                    selectedMaterials.Add(material);
                }
            }

            return selectedMaterials;
        }
    }

    /// <summary>
    /// 物料选择项
    /// </summary>
    public partial class MaterialSelectionItem : ObservableObject
    {
        [ObservableProperty]
        private string name;

        [ObservableProperty]
        private string category;

        [ObservableProperty]
        private bool isSelected;

        [ObservableProperty]
        private double price;

        public MaterialSelectionItem(string name, string category, bool isSelected, double price)
        {
            this.name = name;
            this.category = category;
            this.isSelected = isSelected;
            this.price = price;
        }

        public string PriceText => Price > 0 ? $"{Price:F2}元/吨" : "价格待定";

        public string SelectionStatus => IsSelected ? "已选" : "未选";

        public Brush StatusColor => IsSelected ? Brushes.Green : Brushes.Gray;


    }
}
